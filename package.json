{"name": "contract-dashboard", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build --turbopack", "start": "next start", "lint": "eslint"}, "dependencies": {"@react-pdf/renderer": "^4.3.0", "convex": "^1.26.1", "html2canvas": "^1.4.1", "jspdf": "^3.0.2", "lucide-react": "^0.541.0", "next": "15.5.0", "react": "19.1.0", "react-dom": "19.1.0", "react-pdf": "^10.1.0"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.5.0", "tailwindcss": "^4", "typescript": "^5"}}